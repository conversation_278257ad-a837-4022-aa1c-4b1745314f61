rules_version = '2';

service firebase.storage {
  match /b/{bucket}/o {
    // Users can only upload and access their own receipt images
    match /receipts/{userId}/{allPaths=**} {
      // Allow read access for authenticated users to their own files
      allow read: if request.auth != null && request.auth.uid == userId;

      // Allow delete for authenticated users on their own files
      allow delete: if request.auth != null && request.auth.uid == userId;

      // Allow create/update with strict validation (size and content type)
      allow create, update: if request.auth != null
        && request.auth.uid == userId
        && request.resource != null
        && request.resource.size < 10 * 1024 * 1024  // 10MB limit
        && request.resource.contentType.matches('image/.*');  // Only images
    }
    
    // Deny all other access
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
