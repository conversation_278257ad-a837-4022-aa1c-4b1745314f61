# Copy this file to .env.local and fill in your actual values

# SECURITY NOTE: Do NOT add GEMINI_API_KEY here!
# The Gemini API key should be stored as a Firebase Functions secret
# and accessed only from server-side Cloud Functions to prevent exposure.
# See DEPLOYMENT_GUIDE.md for instructions on setting up Functions secrets.

# Firebase Configuration
# Get these values from your Firebase project settings
VITE_FIREBASE_API_KEY=your_firebase_api_key_here
VITE_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id_here
VITE_FIREBASE_STORAGE_BUCKET=your_project_id.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id_here
VITE_FIREBASE_APP_ID=your_app_id_here
