import React from 'react';
import { Account, Transaction } from '../../types';
import { TransactionItem } from './TransactionItem';

interface DashboardScreenProps {
  accounts: Account[];
  calculateBalance: (accountId: string) => number;
  totalBalance: number;
  transactions: Transaction[];
  openTransactionModal: (tx?: Transaction) => void;
  formatCurrency: (amount: number) => string;
}

export const DashboardScreen = React.memo<DashboardScreenProps>(
  ({
    accounts,
    calculateBalance,
    totalBalance,
    transactions,
    openTransactionModal,
    formatCurrency,
  }) => {
    return (
      <div className="animate-fade-in">
        <header className="mb-8">
          <h1 className="text-base text-gray-400">Total Balance</h1>
          <p className="text-4xl font-bold tracking-tight text-gray-100">
            {formatCurrency(totalBalance)}
          </p>
        </header>

        <section className="mb-8">
          <h2 className="text-lg font-semibold text-gray-300 mb-3">Accounts</h2>
          <div className="space-y-3">
            {accounts.length > 0 ? (
              accounts.map(acc => (
                <div key={acc.id} className="bg-gray-900 p-4 rounded-xl border border-gray-800">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-semibold text-gray-100">{acc.name}</p>
                      <p className="text-sm text-gray-400">{acc.type}</p>
                    </div>
                    <p className="text-lg font-mono font-semibold text-gray-200">
                      {formatCurrency(calculateBalance(acc.id))}
                    </p>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-center text-gray-500 py-4">
                No accounts yet. Go to Settings to add one.
              </p>
            )}
          </div>
        </section>

        <section>
          <h2 className="text-lg font-semibold text-gray-300 mb-3">Recent Transactions</h2>
          <div className="space-y-2">
            {transactions.length > 0 ? (
              transactions.map(tx => (
                <TransactionItem
                  key={tx.id}
                  transaction={tx}
                  account={accounts.find(a => a.id === tx.accountId)}
                  onClick={() => openTransactionModal(tx)}
                  formatCurrency={formatCurrency}
                />
              ))
            ) : (
              <p className="text-center text-gray-500 py-4">No transactions yet.</p>
            )}
          </div>
        </section>
      </div>
    );
  }
);

DashboardScreen.displayName = 'DashboardScreen';
