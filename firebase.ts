import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';
import { getFunctions } from 'firebase/functions';

// CRITICAL: Replace this configuration with your actual Firebase project settings
//
// To get your Firebase configuration:
// 1. Go to https://console.firebase.google.com/
// 2. Select your project (or create a new one)
// 3. Go to Project Settings (gear icon)
// 4. Scroll down to "Your apps" section
// 5. Click on the web app icon (</>)
// 6. Copy the firebaseConfig object and replace the one below
//
// SECURITY NOTE: These values are safe to expose in client-side code
// as they are used for client identification, not authentication.
const firebaseConfig = {
  apiKey: process.env.FIREBASE_API_KEY || 'PASTE_YOUR_API_KEY_HERE',
  authDomain: process.env.FIREBASE_AUTH_DOMAIN || 'PASTE_YOUR_AUTH_DOMAIN_HERE',
  projectId: process.env.FIREBASE_PROJECT_ID || 'PASTE_YOUR_PROJECT_ID_HERE',
  storageBucket: process.env.FIREBASE_STORAGE_BUCKET || 'PASTE_YOUR_STORAGE_BUCKET_HERE',
  messagingSenderId:
    process.env.FIREBASE_MESSAGING_SENDER_ID || 'PASTE_YOUR_MESSAGING_SENDER_ID_HERE',
  appId: process.env.FIREBASE_APP_ID || 'PASTE_YOUR_APP_ID_HERE',
};

// Validate configuration
const requiredFields = [
  'apiKey',
  'authDomain',
  'projectId',
  'storageBucket',
  'messagingSenderId',
  'appId',
];
const missingFields = requiredFields.filter(
  field =>
    !firebaseConfig[field as keyof typeof firebaseConfig] ||
    firebaseConfig[field as keyof typeof firebaseConfig].includes('PASTE_YOUR_')
);

if (missingFields.length > 0) {
  const errorMessage = `❌ Firebase Configuration Error: Missing or invalid fields: ${missingFields.join(', ')}. Please update firebase.ts with your actual Firebase project configuration.`;

  // eslint-disable-next-line no-console
  console.error(errorMessage);
  // eslint-disable-next-line no-console
  console.error('See comments in firebase.ts for instructions.');

  // Fail fast in production to prevent silent failures
  if (process.env.NODE_ENV === 'production') {
    throw new Error(errorMessage);
  }
}

// Initialize Firebase
export const app = initializeApp(firebaseConfig);
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);
export const functions = getFunctions(app);
